<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="noiseFilter">
      <feTurbulence type="fractalNoise" baseFrequency="0.85" numOctaves="3" stitchTiles="stitch" seed="2"/>
      <feColorMatrix type="saturate" values="0"/>
      <feComponentTransfer>
        <feFuncA type="discrete" tableValues="0.05 0.1 0.15 0.2 0.25 0.3"/>
      </feComponentTransfer>
      <feComposite operator="multiply" in2="SourceGraphic"/>
    </filter>

    <filter id="grainFilter">
      <feTurbulence type="turbulence" baseFrequency="0.95" numOctaves="2" stitchTiles="stitch" seed="5"/>
      <feColorMatrix type="saturate" values="0"/>
      <feComponentTransfer>
        <feFuncA type="discrete" tableValues="0.02 0.05 0.08 0.1"/>
      </feComponentTransfer>
    </filter>
  </defs>

  <!-- Base noise layer -->
  <rect width="100%" height="100%" fill="#000" filter="url(#noiseFilter)" opacity="0.4"/>

  <!-- Fine grain overlay -->
  <rect width="100%" height="100%" fill="#fff" filter="url(#grainFilter)" opacity="0.2"/>
</svg>
